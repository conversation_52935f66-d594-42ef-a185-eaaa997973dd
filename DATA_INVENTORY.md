# TCGA Radiology Data Inventory

## Overview
This document provides a comprehensive inventory of all TCGA radiology data available in this workspace, including original DICOM files, processed images, extracted patches, and metadata.

## Data Summary
- **3 patients** from TCGA-READ collection
- **2 modalities**: CT and MRI
- **383+ original DICOM files**
- **3 processed NIfTI images** (335.6 MB total)
- **1,288 extracted patches** for AI training (1.3 GB total)
- **100% processing success rate**

---

## 1. Original DICOM Data

**Location**: `manifest-5OJ3bOs54237323096513094694/`  
**Format**: DICOM (.dcm files)  
**Structure**: Collection → Patient → Study → Series → DICOM files

### Patients and Studies:

#### TCGA-BM-6198
- **MRI Study**: `02-09-1991-NA-MRI ABDOMEN PELVIS WWO CONT-29917`
  - 27 series with various MRI sequences (T1, T2, etc.)
  - ~2,000+ MR DICOM files
- **CT Study**: `03-09-1991-NA-CT ABDOMEN PELVIS W CONT-44892`
  - 2 series: Topogram (2 files) + Abdomen/Pelvis (106 files)
  - 108 CT DICOM files

#### TCGA-CL-4957
- **CT Study**: `08-24-1997-NA-CT ABDOMEN W CO-74402`
  - 3 series: Topogram (1 file) + ABDPEL axial (79 files) + ABDPEL coronal (81 files)
  - 161 CT DICOM files

#### TCGA-CL-5917
- **CT Study**: `04-25-1995-NA-CT008 CT ABDOMEN W CO-39508`
  - 2 series: Topogram (1 file) + AP I (89 files)
  - 90 CT DICOM files

---

## 2. Metadata CSV

**File**: `manifest-5OJ3bOs54237323096513094694/metadata.csv`  
**Format**: CSV  
**Entries**: 34 series  
**Columns**: 17 columns including:
- Series UID, Patient ID, Study Description
- Modality, Manufacturer, Series Description
- Number of Images, File Size, File Location

**Modalities**: CT, MR

---

## 3. Processed NIfTI Images

**Location**: `processed_tcga_data/processed/`  
**Format**: NIfTI (.nii.gz) - Medical imaging standard  
**Processing**: DICOM → Standardized → Resampled → Intensity normalized

### Files:

| File | Size | Dimensions | Voxel Size | Data Type | Value Range |
|------|------|------------|------------|-----------|-------------|
| `TCGA-BM-6198_CT.nii.gz` | 159.7 MB | 500×500×530 | 1.0×1.0×1.0 mm | float64 | [0.000, 1.000] |
| `TCGA-CL-4957_CT.nii.gz` | 118.0 MB | 447×447×416 | 1.0×1.0×1.0 mm | float64 | [0.000, 1.000] |
| `TCGA-CL-5917_CT.nii.gz` | 57.9 MB | 317×317×445 | 1.0×1.0×1.0 mm | float64 | [0.000, 1.000] |

**Features**:
- ✅ Standardized LPS orientation
- ✅ Isotropic 1mm voxel spacing
- ✅ Intensity normalized to [0, 1] range
- ✅ Ready for medical image analysis

---

## 4. PyTorch Patch Tensors

**Location**: `processed_tcga_data/patches/`  
**Format**: PyTorch tensors (.pt files)  
**Purpose**: Ready-to-use patches for deep learning training

### Files:

| File | Size | Shape | Patches | Patch Size | Data Type | Value Range |
|------|------|-------|---------|------------|-----------|-------------|
| `TCGA-BM-6198_CT.pt` | 500.0 MB | [500, 64, 64, 64] | 500 | 64³ voxels | float32 | [0.000, 1.000] |
| `TCGA-CL-4957_CT.pt` | 500.0 MB | [500, 64, 64, 64] | 500 | 64³ voxels | float32 | [0.000, 1.000] |
| `TCGA-CL-5917_CT.pt` | 288.0 MB | [288, 64, 64, 64] | 288 | 64³ voxels | float32 | [0.000, 1.000] |

**Total**: 1,288 patches (1.3 GB)

**Features**:
- ✅ 64×64×64 voxel patches (4.1mm³ physical size)
- ✅ 25% overlap between adjacent patches
- ✅ Only patches with >5% non-zero content included
- ✅ PyTorch-ready format for immediate training

---

## 5. Quality Control Reports

**Location**: `processed_tcga_data/qc/`  
**Format**: JSON  
**Purpose**: Automated quality assessment of processed images

### QC Metrics:

| Patient | Status | SNR | Contrast | Image Size | Notes |
|---------|--------|-----|----------|------------|-------|
| TCGA-BM-6198 | ✅ PASS | 1.06 | 1.00 | [500, 500, 530] | Good quality CT |
| TCGA-CL-4957 | ✅ PASS | 1.68 | 1.00 | [447, 447, 416] | High quality CT |
| TCGA-CL-5917 | ✅ PASS | 1.03 | 1.00 | [317, 317, 445] | Acceptable quality CT |

**QC Criteria**:
- Signal-to-noise ratio > 1.0
- Contrast > 0.01
- Non-zero voxels > 5%
- All processed images passed QC ✅

---

## 6. AI Training Metadata

**File**: `processed_tcga_data/metadata.json`  
**Format**: JSON  
**Purpose**: Maps patients to their processed patch files for AI training

```json
{
  "ct": {
    "TCGA-BM-6198": {
      "num_patches": 500,
      "file": "TCGA-BM-6198_CT.pt"
    },
    "TCGA-CL-4957": {
      "num_patches": 500,
      "file": "TCGA-CL-4957_CT.pt"
    },
    "TCGA-CL-5917": {
      "num_patches": 288,
      "file": "TCGA-CL-5917_CT.pt"
    }
  }
}
```

**Structure**: `Modality → Patient ID → {num_patches, file}`

---

## 7. Processing Summary

**File**: `processed_tcga_data/processing_summary.json`  
**Format**: JSON

**Key Statistics**:
- Status: ✅ Completed
- Total patients: 3
- Successful: 3 (100%)
- Failed: 0 (0%)
- Total patches extracted: 1,288
- Average processing time: 72.3 seconds/patient

---

## 8. Processing Logs

**Location**: `processed_tcga_data/logs/`  
**Format**: Text logs  
**Files**: 3 log files (9.6 KB total)

Contains detailed processing information including:
- DICOM loading status
- Preprocessing steps
- QC results
- Error messages (if any)
- Performance metrics

---

## Data Usage Examples

### Loading Patches for Training:
```python
import torch

# Load patches for a patient
patches = torch.load('processed_tcga_data/patches/TCGA-BM-6198_CT.pt')
print(f"Shape: {patches.shape}")  # [500, 64, 64, 64]
print(f"Data type: {patches.dtype}")  # torch.float32
```

### Loading Processed Images:
```python
import nibabel as nib

# Load processed NIfTI image
img = nib.load('processed_tcga_data/processed/TCGA-BM-6198_CT.nii.gz')
data = img.get_fdata()
print(f"Shape: {data.shape}")  # (500, 500, 530)
```

### Using Metadata:
```python
import json

# Load training metadata
with open('processed_tcga_data/metadata.json', 'r') as f:
    metadata = json.load(f)

# Get all CT patients
ct_patients = metadata['ct']
print(f"CT patients: {len(ct_patients)}")
```

---

## File Size Summary

| Category | Files | Total Size |
|----------|-------|------------|
| Original DICOM | 383+ files | ~2-3 GB |
| Processed NIfTI | 3 files | 335.6 MB |
| PyTorch Patches | 3 files | 1.3 GB |
| QC Reports | 3 files | <1 MB |
| Logs & Metadata | 6 files | <1 MB |
| **Total** | **398+ files** | **~4-5 GB** |

---

## Ready for AI Training! 🚀

All data has been successfully preprocessed and is ready for:
- ✅ Deep learning model training
- ✅ Medical image analysis
- ✅ Computer vision research
- ✅ AI model development
