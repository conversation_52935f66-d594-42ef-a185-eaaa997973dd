#!/usr/bin/env python3
"""
Simple runner script for TCGA Data Analyzer.
Use this to test the analyzer before building the standalone executable.
"""

import sys
import os
from pathlib import Path

# Add current directory to path so we can import the analyzer
sys.path.insert(0, str(Path(__file__).parent))

try:
    from tcga_data_analyzer import main
    
    if __name__ == "__main__":
        print("Running TCGA Data Analyzer...")
        print("=" * 50)
        main()
        
except ImportError as e:
    print(f"Error importing analyzer: {e}")
    print("Make sure tcga_data_analyzer.py is in the same directory.")
    sys.exit(1)
except Exception as e:
    print(f"Error running analyzer: {e}")
    sys.exit(1)
