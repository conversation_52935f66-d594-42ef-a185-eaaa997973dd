#!/usr/bin/env python3
"""
Simple script to run TCGA radiology preprocessing with default settings.

Usage:
    python run_preprocessing.py

This script will:
1. Process all TCGA radiology data in the current directory
2. Apply standardization, resampling, intensity normalization
3. Perform registration and skull stripping where appropriate
4. Extract patches for AI training
5. Generate metadata JSON file
6. Perform quality control checks
"""

import os
import sys
from pathlib import Path
from tcga_radiology_preprocessor import TCGARadiologyPreprocessor

def main():
    """Run preprocessing with default settings."""
    
    # Set up paths
    current_dir = Path.cwd()
    input_dir = current_dir / "manifest-5OJ3bOs54237323096513094694"
    output_dir = current_dir / "processed_tcga_data"
    
    # Check if input directory exists
    if not input_dir.exists():
        print(f"Error: Input directory not found: {input_dir}")
        print("Please ensure the TCGA data is in the expected location.")
        sys.exit(1)
    
    print("TCGA Radiology Data Preprocessing")
    print("=" * 50)
    print(f"Input directory: {input_dir}")
    print(f"Output directory: {output_dir}")
    print()
    
    # Initialize preprocessor with optimized settings
    preprocessor = TCGARadiologyPreprocessor(
        input_dir=str(input_dir),
        output_dir=str(output_dir),
        target_spacing=(1.0, 1.0, 1.0),  # 1mm isotropic
        target_size=None,  # Keep original size after resampling
        intensity_range=(0.0, 1.0),  # Normalize to [0, 1]
        patch_size=(64, 64, 64),  # 64x64x64 patches
        patch_overlap=0.25  # 25% overlap between patches
    )
    
    # Run preprocessing
    try:
        print("Starting preprocessing pipeline...")
        summary = preprocessor.run_preprocessing()
        
        if summary['status'] == 'completed':
            print("\n" + "="*60)
            print("PREPROCESSING COMPLETED SUCCESSFULLY!")
            print("="*60)
            print(f"✓ Processed: {summary['successful_patients']}/{summary['total_patients']} patients")
            print(f"✓ Total patches extracted: {summary['total_patches_extracted']}")
            print(f"✓ Average processing time: {summary['average_processing_time_per_patient']:.2f} seconds/patient")
            print(f"✓ Output directory: {summary['output_directory']}")
            print(f"✓ Metadata file: {summary['metadata_file']}")
            
            # Show directory structure
            print("\nOutput directory structure:")
            print("├── processed/          # Preprocessed NIfTI images")
            print("├── patches/           # Extracted patches (.pt files)")
            print("├── qc/               # Quality control reports")
            print("├── logs/             # Processing logs")
            print("├── metadata.json     # Final metadata for AI training")
            print("└── processing_summary.json")
            
            print("\nMetadata JSON format:")
            print('{')
            print('  "ct": {')
            print('    "TCGA-XX-XXXX": {')
            print('      "num_patches": 1234,')
            print('      "file": "TCGA-XX-XXXX_CT.pt"')
            print('    }')
            print('  },')
            print('  "mr": {')
            print('    "TCGA-YY-YYYY": {')
            print('      "num_patches": 567,')
            print('      "file": "TCGA-YY-YYYY_MR.pt"')
            print('    }')
            print('  }')
            print('}')
            
            print("\n" + "="*60)
            print("Ready for AI training! 🚀")
            print("="*60)
            
        else:
            print(f"❌ Preprocessing failed: {summary.get('error', 'Unknown error')}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n❌ Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
