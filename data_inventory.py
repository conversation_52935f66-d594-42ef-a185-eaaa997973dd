#!/usr/bin/env python3
"""
Create a comprehensive inventory of all TCGA radiology data and formats.
"""

import json
import torch
import nibabel as nib
import pydicom
import pandas as pd
from pathlib import Path
import os

def analyze_data():
    """Analyze all available data and create inventory."""
    
    print("=== TCGA RADIOLOGY DATA INVENTORY ===")
    print()

    # 1. Original DICOM Data
    print("1. ORIGINAL DICOM DATA:")
    print("   Location: manifest-5OJ3bOs54237323096513094694/")
    print("   Format: DICOM (.dcm files)")
    print()

    manifest_dir = Path("manifest-5OJ3bOs54237323096513094694")
    if manifest_dir.exists():
        patients = list(manifest_dir.glob("*/TCGA-*"))
        total_dicom_files = 0
        modalities = set()
        
        for patient_dir in patients:
            patient_id = patient_dir.name
            studies = list(patient_dir.glob("*"))
            
            print(f"   Patient: {patient_id}")
            for study_dir in studies:
                if study_dir.is_dir():
                    study_name = study_dir.name
                    series_dirs = [d for d in study_dir.glob("*") if d.is_dir()]
                    
                    print(f"     Study: {study_name[:60]}...")
                    for series_dir in series_dirs[:2]:  # Show first 2 series
                        series_name = series_dir.name
                        dicom_files = list(series_dir.glob("*.dcm"))
                        total_dicom_files += len(dicom_files)
                        
                        if dicom_files:
                            try:
                                dcm = pydicom.dcmread(str(dicom_files[0]), stop_before_pixels=True)
                                modality = dcm.get("Modality", "Unknown")
                                modalities.add(modality)
                                print(f"       Series: {series_name[:40]}... ({len(dicom_files)} {modality} files)")
                            except:
                                print(f"       Series: {series_name[:40]}... ({len(dicom_files)} files)")
                    
                    if len(series_dirs) > 2:
                        print(f"       ... and {len(series_dirs)-2} more series")
            print()
        
        print(f"   Summary: {len(patients)} patients, {total_dicom_files}+ DICOM files")
        print(f"   Modalities found: {sorted(modalities)}")
        print()

    # 2. Metadata CSV
    print("2. METADATA CSV:")
    metadata_csv = manifest_dir / "metadata.csv"
    if metadata_csv.exists():
        df = pd.read_csv(metadata_csv)
        print(f"   File: {metadata_csv}")
        print(f"   Format: CSV")
        print(f"   Entries: {len(df)} series")
        print(f"   Columns: {len(df.columns)} columns")
        modalities_csv = sorted(df["Modality"].unique())
        print(f"   Modalities: {modalities_csv}")
        print()

    # 3. Processed NIfTI Images
    print("3. PROCESSED NIFTI IMAGES:")
    processed_dir = Path("processed_tcga_data/processed")
    if processed_dir.exists():
        print(f"   Location: {processed_dir}")
        print(f"   Format: NIfTI (.nii.gz)")
        print()
        
        for nii_file in sorted(processed_dir.glob("*.nii.gz")):
            try:
                img = nib.load(str(nii_file))
                data = img.get_fdata()
                header = img.header
                
                size_mb = nii_file.stat().st_size / 1024 / 1024
                print(f"   File: {nii_file.name}")
                print(f"     Size: {size_mb:.1f} MB")
                print(f"     Dimensions: {data.shape}")
                print(f"     Voxel size: {header.get_zooms()[:3]} mm")
                print(f"     Data type: {data.dtype}")
                print(f"     Value range: [{data.min():.3f}, {data.max():.3f}]")
                print()
            except Exception as e:
                print(f"   File: {nii_file.name} - Error: {e}")
        print()

    # 4. PyTorch Patch Tensors
    print("4. PYTORCH PATCH TENSORS:")
    patches_dir = Path("processed_tcga_data/patches")
    if patches_dir.exists():
        print(f"   Location: {patches_dir}")
        print(f"   Format: PyTorch tensors (.pt files)")
        print()
        
        total_patches = 0
        for pt_file in sorted(patches_dir.glob("*.pt")):
            try:
                patches = torch.load(pt_file)
                size_mb = pt_file.stat().st_size / 1024 / 1024
                total_patches += len(patches)
                
                print(f"   File: {pt_file.name}")
                print(f"     Size: {size_mb:.1f} MB")
                print(f"     Shape: {patches.shape}")
                print(f"     Data type: {patches.dtype}")
                print(f"     Number of patches: {len(patches)}")
                print(f"     Patch size: {patches.shape[1:]}")
                print(f"     Value range: [{patches.min():.3f}, {patches.max():.3f}]")
                print()
            except Exception as e:
                print(f"   File: {pt_file.name} - Error: {e}")
        
        print(f"   Total patches across all files: {total_patches}")
        print()

    # 5. Quality Control Reports
    print("5. QUALITY CONTROL REPORTS:")
    qc_dir = Path("processed_tcga_data/qc")
    if qc_dir.exists():
        print(f"   Location: {qc_dir}")
        print(f"   Format: JSON")
        print()
        
        for qc_file in sorted(qc_dir.glob("*.json")):
            with open(qc_file, 'r') as f:
                qc_data = json.load(f)
            
            print(f"   File: {qc_file.name}")
            print(f"     Patient: {qc_data['patient_id']}")
            print(f"     QC Status: {'PASS' if qc_data['pass_qc'] else 'FAIL'}")
            print(f"     SNR: {qc_data['snr']:.2f}")
            print(f"     Contrast: {qc_data['contrast']:.2f}")
            print(f"     Image size: {qc_data['image_size']}")
            print(f"     Voxel spacing: {qc_data['image_spacing']}")
            print()

    # 6. Metadata for AI Training
    print("6. METADATA FOR AI TRAINING:")
    metadata_file = Path("processed_tcga_data/metadata.json")
    if metadata_file.exists():
        with open(metadata_file, 'r') as f:
            metadata = json.load(f)
        
        print(f"   File: {metadata_file}")
        print(f"   Format: JSON")
        print(f"   Structure: Modality -> Patient -> Patch info")
        print()
        
        for modality, patients in metadata.items():
            print(f"   Modality: {modality.upper()}")
            total_patches = sum(p["num_patches"] for p in patients.values())
            print(f"     Patients: {len(patients)}")
            print(f"     Total patches: {total_patches}")
            for patient_id, info in patients.items():
                print(f"       {patient_id}: {info['num_patches']} patches -> {info['file']}")
            print()

    # 7. Processing Summary
    print("7. PROCESSING SUMMARY:")
    summary_file = Path("processed_tcga_data/processing_summary.json")
    if summary_file.exists():
        with open(summary_file, 'r') as f:
            summary = json.load(f)
        
        print(f"   File: {summary_file}")
        print(f"   Format: JSON")
        print(f"   Status: {summary['status']}")
        print(f"   Total patients: {summary['total_patients']}")
        print(f"   Successful: {summary['successful_patients']}")
        print(f"   Failed: {summary['failed_patients']}")
        print(f"   Total patches: {summary['total_patches_extracted']}")
        print(f"   Avg processing time: {summary['average_processing_time_per_patient']:.1f} sec/patient")
        print()

    # 8. Processing Logs
    print("8. PROCESSING LOGS:")
    logs_dir = Path("processed_tcga_data/logs")
    if logs_dir.exists():
        print(f"   Location: {logs_dir}")
        print(f"   Format: Text logs")
        
        for log_file in sorted(logs_dir.glob("*.log")):
            size_kb = log_file.stat().st_size / 1024
            print(f"   File: {log_file.name} ({size_kb:.1f} KB)")
        print()

if __name__ == "__main__":
    analyze_data()
