#!/usr/bin/env python3
"""
TCGA Data Analyzer - Comprehensive inventory tool for TCGA radiology data
Handles any number of XXXX-Opened directories containing TCGA data.
Can be bundled as a standalone executable using PyInstaller.
"""

import json
import os
import sys
from pathlib import Path
from collections import defaultdict
import argparse
from datetime import datetime

# Optional imports with fallbacks
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("Warning: pandas not available. CSV analysis will be limited.")

try:
    import pydicom
    HAS_PYDICOM = True
except ImportError:
    HAS_PYDICOM = False
    print("Warning: pydicom not available. DICOM analysis will be limited.")

try:
    import nibabel as nib
    HAS_NIBABEL = True
except ImportError:
    HAS_NIBABEL = False
    print("Warning: nibabel not available. NIfTI analysis will be limited.")

try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False
    print("Warning: torch not available. PyTorch tensor analysis will be limited.")


class TCGADataAnalyzer:
    """Comprehensive TCGA data analyzer for any number of XXXX-Opened directories."""
    
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        self.results = {
            'scan_timestamp': datetime.now().isoformat(),
            'base_directory': str(self.base_dir.absolute()),
            'opened_directories': [],
            'summary': defaultdict(int),
            'errors': []
        }
    
    def find_opened_directories(self):
        """Find all XXXX-Opened directories in the base directory."""
        opened_dirs = []
        for item in self.base_dir.iterdir():
            if item.is_dir() and item.name.endswith('-Opened'):
                opened_dirs.append(item)
        return sorted(opened_dirs)
    
    def analyze_dicom_files(self, directory):
        """Analyze DICOM files in a directory."""
        dicom_info = {
            'total_files': 0,
            'modalities': set(),
            'patients': [],
            'series_count': 0,
            'studies_count': 0
        }
        
        # Look for DICOM files recursively
        dicom_files = list(directory.rglob("*.dcm"))
        dicom_info['total_files'] = len(dicom_files)
        
        # Analyze directory structure
        tcga_dirs = list(directory.rglob("TCGA-*"))
        patient_dirs = [d for d in tcga_dirs if d.is_dir() and d.parent.name.startswith('TCGA-')]
        
        for patient_dir in patient_dirs:
            patient_id = patient_dir.name
            studies = [d for d in patient_dir.iterdir() if d.is_dir()]
            
            patient_info = {
                'patient_id': patient_id,
                'studies_count': len(studies),
                'series_count': 0,
                'modalities': set()
            }
            
            for study_dir in studies:
                series_dirs = [d for d in study_dir.iterdir() if d.is_dir()]
                patient_info['series_count'] += len(series_dirs)
                dicom_info['series_count'] += len(series_dirs)
                
                # Sample DICOM files for modality detection
                if HAS_PYDICOM:
                    for series_dir in series_dirs[:3]:  # Sample first 3 series
                        series_dicom_files = list(series_dir.glob("*.dcm"))
                        if series_dicom_files:
                            try:
                                dcm = pydicom.dcmread(str(series_dicom_files[0]), stop_before_pixels=True)
                                modality = dcm.get("Modality", "Unknown")
                                patient_info['modalities'].add(modality)
                                dicom_info['modalities'].add(modality)
                            except Exception as e:
                                self.results['errors'].append(f"Error reading DICOM {series_dicom_files[0]}: {e}")
            
            dicom_info['studies_count'] += len(studies)
            patient_info['modalities'] = list(patient_info['modalities'])
            dicom_info['patients'].append(patient_info)
        
        dicom_info['modalities'] = list(dicom_info['modalities'])
        return dicom_info
    
    def analyze_csv_files(self, directory):
        """Analyze CSV files in the directory."""
        csv_info = {
            'files': [],
            'total_files': 0
        }
        
        csv_files = list(directory.rglob("*.csv"))
        csv_info['total_files'] = len(csv_files)
        
        if HAS_PANDAS:
            for csv_file in csv_files:
                try:
                    df = pd.read_csv(csv_file)
                    file_info = {
                        'filename': csv_file.name,
                        'path': str(csv_file.relative_to(self.base_dir)),
                        'rows': len(df),
                        'columns': len(df.columns),
                        'column_names': list(df.columns)
                    }
                    
                    # Check for common TCGA columns
                    if 'Modality' in df.columns:
                        file_info['modalities'] = sorted(df['Modality'].unique().tolist())
                    if 'Patient ID' in df.columns or 'PatientID' in df.columns:
                        patient_col = 'Patient ID' if 'Patient ID' in df.columns else 'PatientID'
                        file_info['unique_patients'] = df[patient_col].nunique()
                    
                    csv_info['files'].append(file_info)
                except Exception as e:
                    self.results['errors'].append(f"Error reading CSV {csv_file}: {e}")
        
        return csv_info
    
    def analyze_processed_data(self, directory):
        """Analyze processed data files (NIfTI, PyTorch tensors, etc.)."""
        processed_info = {
            'nifti_files': [],
            'pytorch_files': [],
            'json_files': [],
            'other_files': []
        }
        
        # NIfTI files
        nifti_files = list(directory.rglob("*.nii.gz")) + list(directory.rglob("*.nii"))
        if HAS_NIBABEL:
            for nii_file in nifti_files:
                try:
                    img = nib.load(str(nii_file))
                    data = img.get_fdata()
                    header = img.header
                    
                    file_info = {
                        'filename': nii_file.name,
                        'path': str(nii_file.relative_to(self.base_dir)),
                        'size_mb': nii_file.stat().st_size / 1024 / 1024,
                        'dimensions': data.shape,
                        'voxel_size': header.get_zooms()[:3] if hasattr(header, 'get_zooms') else None,
                        'data_type': str(data.dtype),
                        'value_range': [float(data.min()), float(data.max())]
                    }
                    processed_info['nifti_files'].append(file_info)
                except Exception as e:
                    self.results['errors'].append(f"Error reading NIfTI {nii_file}: {e}")
        else:
            processed_info['nifti_files'] = [{'filename': f.name, 'path': str(f.relative_to(self.base_dir))} for f in nifti_files]
        
        # PyTorch tensor files
        pt_files = list(directory.rglob("*.pt")) + list(directory.rglob("*.pth"))
        if HAS_TORCH:
            for pt_file in pt_files:
                try:
                    tensor = torch.load(pt_file, map_location='cpu')
                    file_info = {
                        'filename': pt_file.name,
                        'path': str(pt_file.relative_to(self.base_dir)),
                        'size_mb': pt_file.stat().st_size / 1024 / 1024,
                        'shape': list(tensor.shape) if hasattr(tensor, 'shape') else None,
                        'data_type': str(tensor.dtype) if hasattr(tensor, 'dtype') else None
                    }
                    processed_info['pytorch_files'].append(file_info)
                except Exception as e:
                    self.results['errors'].append(f"Error reading PyTorch file {pt_file}: {e}")
        else:
            processed_info['pytorch_files'] = [{'filename': f.name, 'path': str(f.relative_to(self.base_dir))} for f in pt_files]
        
        # JSON files
        json_files = list(directory.rglob("*.json"))
        for json_file in json_files:
            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                file_info = {
                    'filename': json_file.name,
                    'path': str(json_file.relative_to(self.base_dir)),
                    'size_kb': json_file.stat().st_size / 1024,
                    'keys': list(data.keys()) if isinstance(data, dict) else None,
                    'type': type(data).__name__
                }
                processed_info['json_files'].append(file_info)
            except Exception as e:
                self.results['errors'].append(f"Error reading JSON {json_file}: {e}")
        
        return processed_info
    
    def analyze_opened_directory(self, opened_dir):
        """Analyze a single XXXX-Opened directory."""
        print(f"\nAnalyzing {opened_dir.name}...")
        
        dir_info = {
            'directory_name': opened_dir.name,
            'cancer_type': opened_dir.name.replace('-Opened', ''),
            'path': str(opened_dir.relative_to(self.base_dir)),
            'total_size_mb': 0,
            'dicom_analysis': {},
            'csv_analysis': {},
            'processed_analysis': {}
        }
        
        # Calculate total directory size
        try:
            total_size = sum(f.stat().st_size for f in opened_dir.rglob('*') if f.is_file())
            dir_info['total_size_mb'] = total_size / 1024 / 1024
        except Exception as e:
            self.results['errors'].append(f"Error calculating size for {opened_dir}: {e}")
        
        # Analyze DICOM files
        dir_info['dicom_analysis'] = self.analyze_dicom_files(opened_dir)
        
        # Analyze CSV files
        dir_info['csv_analysis'] = self.analyze_csv_files(opened_dir)
        
        # Analyze processed data
        dir_info['processed_analysis'] = self.analyze_processed_data(opened_dir)
        
        # Update summary statistics
        self.results['summary']['total_dicom_files'] += dir_info['dicom_analysis']['total_files']
        self.results['summary']['total_patients'] += len(dir_info['dicom_analysis']['patients'])
        self.results['summary']['total_series'] += dir_info['dicom_analysis']['series_count']
        self.results['summary']['total_studies'] += dir_info['dicom_analysis']['studies_count']
        self.results['summary']['total_size_mb'] += dir_info['total_size_mb']
        
        return dir_info
    
    def run_analysis(self):
        """Run the complete analysis."""
        print("=== TCGA DATA ANALYZER ===")
        print(f"Scanning directory: {self.base_dir.absolute()}")
        print(f"Timestamp: {self.results['scan_timestamp']}")
        
        # Find all XXXX-Opened directories
        opened_dirs = self.find_opened_directories()
        
        if not opened_dirs:
            print("No XXXX-Opened directories found!")
            return self.results
        
        print(f"\nFound {len(opened_dirs)} opened directories:")
        for d in opened_dirs:
            print(f"  - {d.name}")
        
        # Analyze each directory
        for opened_dir in opened_dirs:
            dir_analysis = self.analyze_opened_directory(opened_dir)
            self.results['opened_directories'].append(dir_analysis)
        
        # Print summary
        self.print_summary()
        
        return self.results
    
    def print_summary(self):
        """Print analysis summary."""
        print("\n" + "="*60)
        print("ANALYSIS SUMMARY")
        print("="*60)
        
        summary = self.results['summary']
        print(f"Total directories analyzed: {len(self.results['opened_directories'])}")
        print(f"Total DICOM files: {summary['total_dicom_files']:,}")
        print(f"Total patients: {summary['total_patients']:,}")
        print(f"Total studies: {summary['total_studies']:,}")
        print(f"Total series: {summary['total_series']:,}")
        print(f"Total data size: {summary['total_size_mb']:,.1f} MB ({summary['total_size_mb']/1024:.1f} GB)")
        
        if self.results['errors']:
            print(f"\nErrors encountered: {len(self.results['errors'])}")
            for error in self.results['errors'][:5]:  # Show first 5 errors
                print(f"  - {error}")
            if len(self.results['errors']) > 5:
                print(f"  ... and {len(self.results['errors']) - 5} more errors")
    
    def save_results(self, output_file="tcga_analysis_results.json"):
        """Save results to JSON file."""
        output_path = self.base_dir / output_file
        with open(output_path, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        print(f"\nResults saved to: {output_path}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Analyze TCGA data in XXXX-Opened directories')
    parser.add_argument('--directory', '-d', default='.', 
                       help='Base directory to scan (default: current directory)')
    parser.add_argument('--output', '-o', default='tcga_analysis_results.json',
                       help='Output JSON file name (default: tcga_analysis_results.json)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose output')
    
    args = parser.parse_args()
    
    # Create analyzer and run analysis
    analyzer = TCGADataAnalyzer(args.directory)
    results = analyzer.run_analysis()
    
    # Save results
    analyzer.save_results(args.output)
    
    print(f"\nAnalysis complete! Check {args.output} for detailed results.")


if __name__ == "__main__":
    main()
