#!/usr/bin/env python3
"""
Build script to create a standalone executable for TCGA Data Analyzer.
This script uses PyInstaller to bundle the analyzer with all dependencies.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Check if PyInstaller is installed."""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """Install PyInstaller if not present."""
    print("Installing PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        return True
    except subprocess.CalledProcessError:
        print("Failed to install PyInstaller")
        return False

def create_spec_file():
    """Create a PyInstaller spec file for better control."""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['tcga_data_analyzer.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pandas',
        'pydicom',
        'nibabel',
        'torch',
        'numpy',
        'json',
        'pathlib',
        'collections',
        'datetime',
        'argparse'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'tkinter',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='tcga_data_analyzer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('tcga_analyzer.spec', 'w') as f:
        f.write(spec_content)
    print("Created PyInstaller spec file: tcga_analyzer.spec")

def build_executable():
    """Build the standalone executable."""
    print("Building standalone executable...")
    
    # Clean previous builds
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    
    # Create spec file for better control
    create_spec_file()
    
    # Build using spec file
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--clean",
        "tcga_analyzer.spec"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Build successful!")
            
            # Check if executable was created
            exe_path = Path("dist/tcga_data_analyzer.exe") if os.name == 'nt' else Path("dist/tcga_data_analyzer")
            if exe_path.exists():
                print(f"✓ Executable created: {exe_path.absolute()}")
                print(f"  Size: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
                return True
            else:
                print("✗ Executable not found in expected location")
                return False
        else:
            print("✗ Build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
    except Exception as e:
        print(f"✗ Build error: {e}")
        return False

def create_batch_file():
    """Create a batch file for easy execution on Windows."""
    if os.name == 'nt':  # Windows
        batch_content = '''@echo off
echo TCGA Data Analyzer
echo ==================
echo.
echo Usage examples:
echo   tcga_data_analyzer.exe                    (analyze current directory)
echo   tcga_data_analyzer.exe -d "C:\\Data"       (analyze specific directory)
echo   tcga_data_analyzer.exe -o results.json   (custom output file)
echo   tcga_data_analyzer.exe --help            (show all options)
echo.
pause
'''
        with open('dist/run_analyzer.bat', 'w') as f:
            f.write(batch_content)
        print("✓ Created batch file: dist/run_analyzer.bat")

def create_readme():
    """Create a README file for the executable."""
    readme_content = '''# TCGA Data Analyzer - Standalone Executable

This is a standalone executable version of the TCGA Data Analyzer that can run on any machine without requiring Python to be installed.

## What it does

The analyzer scans for directories ending with "-Opened" (like BLCA-Opened, BRCA-Opened, etc.) and provides comprehensive analysis of:

- DICOM files and metadata
- CSV files with patient/study information  
- Processed NIfTI images
- PyTorch tensor files
- JSON metadata files
- Directory structure and file counts

## Usage

### Basic usage (analyze current directory):
```
tcga_data_analyzer.exe
```

### Analyze specific directory:
```
tcga_data_analyzer.exe -d "C:\\Path\\To\\Your\\Data"
```

### Custom output file:
```
tcga_data_analyzer.exe -o my_results.json
```

### Show all options:
```
tcga_data_analyzer.exe --help
```

## Output

The analyzer creates a detailed JSON report with:
- Summary statistics
- Per-directory analysis
- File inventories
- Error reports
- Processing timestamps

## Requirements

- No Python installation required
- Works on Windows, macOS, and Linux
- Handles any number of XXXX-Opened directories
- Gracefully handles missing optional dependencies

## File Structure Expected

```
Your-Data-Directory/
├── BLCA-Opened/
│   └── manifest-xxxxx/
│       └── TCGA-BLCA/
│           ├── TCGA-Patient1/
│           └── TCGA-Patient2/
├── BRCA-Opened/
│   └── manifest-xxxxx/
│       └── TCGA-BRCA/
└── Other-Cancer-Opened/
```

The analyzer will automatically find and process all directories ending with "-Opened".
'''
    
    with open('dist/README.txt', 'w') as f:
        f.write(readme_content)
    print("✓ Created README: dist/README.txt")

def main():
    """Main build function."""
    print("=== TCGA Data Analyzer - Build Script ===")
    print()
    
    # Check if source file exists
    if not os.path.exists('tcga_data_analyzer.py'):
        print("✗ Source file 'tcga_data_analyzer.py' not found!")
        return False
    
    # Check PyInstaller
    if not check_pyinstaller():
        print("PyInstaller not found. Installing...")
        if not install_pyinstaller():
            print("✗ Failed to install PyInstaller")
            return False
    
    # Build executable
    if build_executable():
        # Create additional files
        create_batch_file()
        create_readme()
        
        print()
        print("=== Build Complete ===")
        print("Files created in 'dist/' directory:")
        
        dist_path = Path("dist")
        if dist_path.exists():
            for file in dist_path.iterdir():
                if file.is_file():
                    size_mb = file.stat().st_size / 1024 / 1024
                    print(f"  - {file.name} ({size_mb:.1f} MB)")
        
        print()
        print("✓ You can now copy the 'dist' folder to any machine and run the analyzer!")
        print("✓ No Python installation required on the target machine.")
        
        return True
    else:
        print("✗ Build failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
