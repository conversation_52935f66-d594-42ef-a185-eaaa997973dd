#!/usr/bin/env python3
"""
Test script to validate the TCGA radiology preprocessing installation.

This script checks:
1. All required dependencies are installed
2. Basic functionality works
3. Input data structure is correct
"""

import sys
import importlib
from pathlib import Path

def test_dependencies():
    """Test if all required dependencies are available."""
    print("Testing dependencies...")
    
    required_packages = [
        'numpy',
        'pandas', 
        'torch',
        'nibabel',
        'pydicom',
        'SimpleITK',
        'scipy',
        'skimage',
        'sklearn',
        'cv2'
    ]
    
    optional_packages = [
        'ants',
        'nipype'
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✓ {package}")
        except ImportError:
            missing_required.append(package)
            print(f"✗ {package} (REQUIRED)")
    
    for package in optional_packages:
        try:
            importlib.import_module(package)
            print(f"✓ {package} (optional)")
        except ImportError:
            missing_optional.append(package)
            print(f"- {package} (optional, not installed)")
    
    if missing_required:
        print(f"\n❌ Missing required packages: {', '.join(missing_required)}")
        print("Please install with: pip install -r requirements.txt")
        return False
    
    if missing_optional:
        print(f"\n⚠️  Optional packages not installed: {', '.join(missing_optional)}")
        print("Advanced features may be limited.")
    
    print("\n✅ All required dependencies are available!")
    return True

def test_data_structure():
    """Test if the input data structure is correct."""
    print("\nTesting data structure...")
    
    expected_dir = Path("manifest-5OJ3bOs54237323096513094694")
    
    if not expected_dir.exists():
        print(f"❌ Expected data directory not found: {expected_dir}")
        print("Please ensure TCGA data is in the correct location.")
        return False
    
    metadata_file = expected_dir / "metadata.csv"
    if not metadata_file.exists():
        print(f"❌ Metadata file not found: {metadata_file}")
        return False
    
    # Check for TCGA data directories
    tcga_dirs = list(expected_dir.glob("TCGA-*"))
    if not tcga_dirs:
        print("❌ No TCGA data directories found")
        return False
    
    print(f"✓ Found data directory: {expected_dir}")
    print(f"✓ Found metadata file: {metadata_file}")
    print(f"✓ Found {len(tcga_dirs)} TCGA collection(s)")
    
    # Check for DICOM files
    dicom_count = 0
    for tcga_dir in tcga_dirs[:1]:  # Check first collection only
        dicom_files = list(tcga_dir.rglob("*.dcm"))
        dicom_count += len(dicom_files)
        if dicom_files:
            print(f"✓ Found {len(dicom_files)} DICOM files in {tcga_dir.name}")
            break
    
    if dicom_count == 0:
        print("❌ No DICOM files found")
        return False
    
    print("\n✅ Data structure looks correct!")
    return True

def test_basic_functionality():
    """Test basic functionality of the preprocessor."""
    print("\nTesting basic functionality...")
    
    try:
        from tcga_radiology_preprocessor import TCGARadiologyPreprocessor
        print("✓ Successfully imported TCGARadiologyPreprocessor")
        
        # Test initialization
        preprocessor = TCGARadiologyPreprocessor(
            input_dir="test_input",
            output_dir="test_output"
        )
        print("✓ Successfully initialized preprocessor")
        
        # Test metadata loading (if data exists)
        if Path("manifest-5OJ3bOs54237323096513094694").exists():
            preprocessor.input_dir = Path("manifest-5OJ3bOs54237323096513094694")
            try:
                df = preprocessor.load_metadata()
                print(f"✓ Successfully loaded metadata ({len(df)} entries)")
            except Exception as e:
                print(f"⚠️  Metadata loading test failed: {e}")
        
        print("\n✅ Basic functionality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("TCGA Radiology Preprocessing - Installation Test")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test dependencies
    if test_dependencies():
        tests_passed += 1
    
    # Test data structure
    if test_data_structure():
        tests_passed += 1
    
    # Test basic functionality
    if test_basic_functionality():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Ready to run preprocessing.")
        print("\nNext steps:")
        print("1. Run: python run_preprocessing.py")
        print("2. Or: python tcga_radiology_preprocessor.py input_dir output_dir")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
