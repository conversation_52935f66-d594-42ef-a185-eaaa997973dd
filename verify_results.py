#!/usr/bin/env python3
"""
Verify the TCGA preprocessing results.
"""

import json
import torch
from pathlib import Path

def verify_results():
    """Verify all preprocessing results."""
    
    print("=== TCGA RADIOLOGY PREPROCESSING RESULTS ===")
    print()

    # Check metadata
    metadata_file = Path("processed_tcga_data/metadata.json")
    if not metadata_file.exists():
        print("ERROR: metadata.json not found!")
        return False

    with open(metadata_file, 'r') as f:
        metadata = json.load(f)

    print("METADATA SUMMARY:")
    total_patients = 0
    total_patches = 0
    
    for modality, patients in metadata.items():
        print(f"  {modality.upper()} modality: {len(patients)} patients")
        modality_patches = sum(p['num_patches'] for p in patients.values())
        total_patches += modality_patches
        total_patients += len(patients)
        print(f"    Total patches: {modality_patches}")
        
        for patient_id, info in patients.items():
            print(f"    - {patient_id}: {info['num_patches']} patches")
    
    print(f"\nOverall: {total_patients} patients, {total_patches} patches")
    print()

    # Verify patch files
    print("PATCH FILE VERIFICATION:")
    patches_dir = Path("processed_tcga_data/patches")
    patch_files_ok = True
    
    for modality, patients in metadata.items():
        for patient_id, info in patients.items():
            patch_file = patches_dir / info['file']
            if patch_file.exists():
                try:
                    patches = torch.load(patch_file)
                    print(f"  OK {info['file']}: {patches.shape} - {patches.dtype}")
                except Exception as e:
                    print(f"  ERROR {info['file']}: {e}")
                    patch_files_ok = False
            else:
                print(f"  ERROR {info['file']}: File not found")
                patch_files_ok = False
    print()

    # Check processed images
    print("PROCESSED IMAGE VERIFICATION:")
    processed_dir = Path("processed_tcga_data/processed")
    image_files_ok = True
    
    for nii_file in sorted(processed_dir.glob("*.nii.gz")):
        size_mb = nii_file.stat().st_size / 1024 / 1024
        print(f"  OK {nii_file.name}: {size_mb:.1f} MB")
    
    if not list(processed_dir.glob("*.nii.gz")):
        print("  ERROR: No processed images found!")
        image_files_ok = False
    print()

    # Check QC reports
    print("QUALITY CONTROL REPORTS:")
    qc_dir = Path("processed_tcga_data/qc")
    qc_files_ok = True
    
    for qc_file in sorted(qc_dir.glob("*.json")):
        try:
            with open(qc_file, 'r') as f:
                qc_data = json.load(f)
            status = "PASS" if qc_data['pass_qc'] else "FAIL"
            snr = qc_data['snr']
            print(f"  {status} {qc_file.stem}: SNR={snr:.2f}")
        except Exception as e:
            print(f"  ERROR {qc_file.name}: {e}")
            qc_files_ok = False
    
    if not list(qc_dir.glob("*.json")):
        print("  ERROR: No QC reports found!")
        qc_files_ok = False

    print()
    
    # Final status
    all_ok = patch_files_ok and image_files_ok and qc_files_ok
    
    if all_ok:
        print("SUCCESS: All preprocessing results verified!")
        print("Ready for AI training!")
        return True
    else:
        print("ERROR: Some issues found in preprocessing results.")
        return False

if __name__ == "__main__":
    verify_results()
