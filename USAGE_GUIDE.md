# TCGA Data Analyzer - Usage Guide

This tool provides comprehensive analysis of TCGA radiology data stored in directories ending with "-Opened" (like BLCA-Opened, BRCA-Opened, etc.).

## Quick Start

### Option 1: Run with Python (if Python is installed)
```bash
python tcga_data_analyzer.py
```

### Option 2: Build Standalone Executable (no Python required on target machine)
```bash
python build_executable.py
```
This creates a `dist/` folder with a standalone executable.

### Option 3: Test Before Building
```bash
python run_analyzer.py
```

## Command Line Options

```bash
# Analyze current directory
tcga_data_analyzer.py

# Analyze specific directory
tcga_data_analyzer.py -d "/path/to/your/data"

# Custom output file
tcga_data_analyzer.py -o "my_analysis.json"

# Verbose output
tcga_data_analyzer.py -v

# Show help
tcga_data_analyzer.py --help
```

## Expected Directory Structure

The analyzer looks for directories ending with "-Opened":

```
Your-Data-Directory/
├── BLCA-Opened/
│   └── manifest-1567090213715/
│       └── TCGA-BLCA/
│           ├── TCGA-4Z-AA7M/
│           │   └── 03-06-2007-NA-TX AS AI-91720/
│           │       ├── 1.000000-SCOUT-38276/
│           │       ├── 2.000000-SC 2.5mm-08283/
│           │       └── ...
│           ├── TCGA-4Z-AA7N/
│           └── ...
├── BRCA-Opened/
│   └── manifest-25vRPwyh8987165612391086998/
│       └── TCGA-BRCA/
│           ├── TCGA-AO-A03M/
│           └── ...
└── OTHER-CANCER-Opened/
    └── ...
```

## What the Analyzer Does

### 1. DICOM File Analysis
- Counts total DICOM files
- Identifies modalities (CT, MR, etc.)
- Analyzes patient/study/series structure
- Extracts metadata from DICOM headers

### 2. CSV/Metadata Analysis
- Finds and analyzes CSV files
- Extracts column information
- Identifies patient counts and modalities
- Handles common TCGA CSV formats

### 3. Processed Data Analysis
- **NIfTI files**: Analyzes dimensions, voxel sizes, data types
- **PyTorch tensors**: Examines tensor shapes and data types
- **JSON files**: Parses metadata and configuration files

### 4. Directory Structure Analysis
- Calculates total data sizes
- Counts files and directories
- Maps patient/study relationships

## Output

The analyzer generates a comprehensive JSON report containing:

```json
{
  "scan_timestamp": "2024-01-15T10:30:00",
  "base_directory": "/path/to/data",
  "opened_directories": [
    {
      "directory_name": "BLCA-Opened",
      "cancer_type": "BLCA",
      "total_size_mb": 15420.5,
      "dicom_analysis": {
        "total_files": 12450,
        "modalities": ["CT", "MR"],
        "patients": [...],
        "series_count": 156,
        "studies_count": 45
      },
      "csv_analysis": {...},
      "processed_analysis": {...}
    }
  ],
  "summary": {
    "total_dicom_files": 25000,
    "total_patients": 120,
    "total_studies": 180,
    "total_series": 450,
    "total_size_mb": 50000.0
  },
  "errors": [...]
}
```

## Dependencies

The analyzer uses graceful degradation - it works even if optional dependencies are missing:

### Required (built into Python)
- `json`, `os`, `sys`, `pathlib`, `collections`, `argparse`, `datetime`

### Optional (enhanced functionality)
- `pandas` - for CSV analysis
- `pydicom` - for DICOM metadata extraction
- `nibabel` - for NIfTI file analysis
- `torch` - for PyTorch tensor analysis

### For Building Executable
- `pyinstaller` - for creating standalone executable

## Installation

### Install Dependencies
```bash
pip install pandas pydicom nibabel torch pyinstaller
```

### Or use existing requirements.txt
```bash
pip install -r requirements.txt
pip install pyinstaller
```

## Building Standalone Executable

1. **Run the build script:**
   ```bash
   python build_executable.py
   ```

2. **Files created in `dist/` folder:**
   - `tcga_data_analyzer.exe` (or `tcga_data_analyzer` on Linux/Mac)
   - `run_analyzer.bat` (Windows helper script)
   - `README.txt` (usage instructions)

3. **Deploy:**
   - Copy the entire `dist/` folder to target machine
   - No Python installation required on target machine
   - Run the executable directly

## Troubleshooting

### Common Issues

1. **"No XXXX-Opened directories found"**
   - Check that your directories end with "-Opened"
   - Verify you're in the correct base directory

2. **"Error reading DICOM files"**
   - Install pydicom: `pip install pydicom`
   - Some DICOM files may be corrupted

3. **"pandas not available"**
   - Install pandas: `pip install pandas`
   - CSV analysis will be limited without pandas

4. **Build fails**
   - Install PyInstaller: `pip install pyinstaller`
   - Check that all source files are present

### Performance Tips

- For large datasets (>100GB), the analysis may take several minutes
- The tool processes files in batches to manage memory usage
- Use `-v` flag for verbose output to monitor progress

## Examples

### Example 1: Basic Analysis
```bash
cd /path/to/tcga/data
python tcga_data_analyzer.py
```

### Example 2: Analyze Remote Directory
```bash
python tcga_data_analyzer.py -d "/mnt/shared/tcga_data" -o "remote_analysis.json"
```

### Example 3: Using Standalone Executable
```bash
# After building
cd dist/
./tcga_data_analyzer --help
./tcga_data_analyzer -d "/data/tcga" -o "results.json"
```

## Integration

The JSON output can be easily integrated with other tools:

```python
import json

# Load results
with open('tcga_analysis_results.json', 'r') as f:
    results = json.load(f)

# Access data
total_patients = results['summary']['total_patients']
cancer_types = [d['cancer_type'] for d in results['opened_directories']]
```

## Support

For issues or questions:
1. Check the error messages in the JSON output
2. Verify directory structure matches expected format
3. Ensure required dependencies are installed
4. Check file permissions for the directories being analyzed
