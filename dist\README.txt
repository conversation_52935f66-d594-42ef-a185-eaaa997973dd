# TCGA Data Analyzer - Standalone Executable

This is a standalone executable version of the TCGA Data Analyzer that can run on any machine without requiring Python to be installed.

## What it does

The analyzer scans for directories ending with "-Opened" (like BLCA-Opened, BRCA-Opened, etc.) and provides comprehensive analysis of:

- DICOM files and metadata
- CSV files with patient/study information
- Processed NIfTI images
- PyTorch tensor files
- JSON metadata files
- Directory structure and file counts

## Usage

### Basic usage (analyze current directory):
tcga_data_analyzer.exe

### Analyze specific directory:
tcga_data_analyzer.exe -d "C:\Path\To\Your\Data"

### Custom output file:
tcga_data_analyzer.exe -o my_results.json

### Show all options:
tcga_data_analyzer.exe --help

## Output

The analyzer creates a detailed JSON report with:
- Summary statistics
- Per-directory analysis
- File inventories
- Error reports
- Processing timestamps

## Requirements

- No Python installation required
- Works on Windows, macOS, and Linux
- Handles any number of XXXX-Opened directories
- Gracefully handles missing optional dependencies

## File Structure Expected

Your-Data-Directory/
├── BLCA-Opened/
│   └── manifest-xxxxx/
│       └── TCGA-BLCA/
│           ├── TCGA-Patient1/
│           └── TCGA-Patient2/
├── BRCA-Opened/
│   └── manifest-xxxxx/
│       └── TCGA-BRCA/
└── Other-Cancer-Opened/

The analyzer will automatically find and process all directories ending with "-Opened".

## Quick Start

1. Copy this entire 'dist' folder to your target machine
2. Open command prompt/terminal in the dist folder
3. Run: tcga_data_analyzer.exe
4. Check the generated JSON file for results

## Windows Users

You can also double-click 'run_analyzer.bat' for a guided experience.