# TCGA Radiology Data Preprocessing Pipeline

A comprehensive Python pipeline for preprocessing TCGA (The Cancer Genome Atlas) radiology data for AI training. This pipeline performs DICOM standardization, resampling, intensity normalization, registration, skull stripping (for MRI), quality control, and patch extraction.

## Features

- **DICOM Standardization**: Converts DICOM files to standardized orientation and format
- **Resampling**: Resamples images to consistent voxel spacing
- **Intensity Normalization**: Modality-specific intensity normalization (CT: HU values, MRI: percentile-based)
- **Registration**: Registers multiple series of the same modality
- **Skull Stripping**: Automatic skull stripping for MRI images
- **Quality Control**: Comprehensive QC checks with automated pass/fail criteria
- **Patch Extraction**: Extracts overlapping patches for deep learning training
- **Metadata Generation**: Creates JSON metadata file mapping patients to processed data

## Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Basic Installation

```bash
# Clone or download the preprocessing scripts
# Install required packages
pip install -r requirements.txt
```

### Advanced Installation (Optional)

For advanced registration and skull stripping features:

```bash
# Install ANTs for advanced registration
pip install antspyx

# Install FSL for advanced skull stripping (requires separate FSL installation)
pip install nipype
```

## Usage

### Quick Start

The simplest way to run the preprocessing:

```bash
python run_preprocessing.py
```

This will process all TCGA data in the `manifest-5OJ3bOs54237323096513094694` directory with default settings.

### Command Line Interface

For more control over the preprocessing parameters:

```bash
python tcga_radiology_preprocessor.py input_dir output_dir [options]
```

#### Parameters

- `input_dir`: Path to TCGA data directory containing the manifest folder
- `output_dir`: Path where processed data will be saved

#### Options

- `--target-spacing X Y Z`: Target voxel spacing in mm (default: 1.0 1.0 1.0)
- `--target-size X Y Z`: Target image size (optional)
- `--intensity-range MIN MAX`: Target intensity range (default: 0.0 1.0)
- `--patch-size X Y Z`: Patch size for extraction (default: 64 64 64)
- `--patch-overlap RATIO`: Overlap ratio between patches (default: 0.5)

#### Example

```bash
python tcga_radiology_preprocessor.py \
    ./manifest-5OJ3bOs54237323096513094694 \
    ./processed_data \
    --target-spacing 1.0 1.0 1.0 \
    --patch-size 128 128 128 \
    --patch-overlap 0.25
```

## Input Data Structure

The pipeline expects TCGA data in the following structure:

```
manifest-5OJ3bOs54237323096513094694/
├── metadata.csv
└── TCGA-READ/
    ├── TCGA-BM-6198/
    │   ├── 02-09-1991-NA-MRI ABDOMEN PELVIS WWO CONT-29917/
    │   │   ├── 9.000000-t1tfltraIP TE 4.1-91997/
    │   │   │   ├── 1-01.dcm
    │   │   │   ├── 1-02.dcm
    │   │   │   └── ...
    │   │   └── ...
    │   └── 03-09-1991-NA-CT ABDOMEN PELVIS W CONT-44892/
    │       └── ...
    └── ...
```

## Output Structure

The pipeline creates the following output structure:

```
output_directory/
├── processed/              # Preprocessed NIfTI images
│   ├── TCGA-BM-6198_CT.nii.gz
│   ├── TCGA-BM-6198_MR.nii.gz
│   └── ...
├── patches/               # Extracted patches as PyTorch tensors
│   ├── TCGA-BM-6198_CT.pt
│   ├── TCGA-BM-6198_MR.pt
│   └── ...
├── qc/                   # Quality control reports
│   ├── TCGA-BM-6198_CT_qc.json
│   └── ...
├── logs/                 # Processing logs
│   └── preprocessing_YYYYMMDD_HHMMSS.log
├── metadata.json         # Final metadata for AI training
└── processing_summary.json
```

## Metadata JSON Format

The generated `metadata.json` file follows the requested format:

```json
{
  "ct": {
    "TCGA-BM-6198": {
      "num_patches": 1234,
      "file": "TCGA-BM-6198_CT.pt"
    },
    "TCGA-CL-4957": {
      "num_patches": 567,
      "file": "TCGA-CL-4957_CT.pt"
    }
  },
  "mr": {
    "TCGA-BM-6198": {
      "num_patches": 890,
      "file": "TCGA-BM-6198_MR.pt"
    }
  }
}
```

## Processing Pipeline

1. **DICOM Loading**: Loads DICOM series and extracts metadata
2. **Standardization**: Converts to standard orientation (LPS)
3. **Resampling**: Resamples to target voxel spacing
4. **Intensity Normalization**: 
   - CT: Clips HU values (-1000 to 1000) and normalizes
   - MRI: Percentile-based robust normalization
5. **Skull Stripping**: For MRI images using threshold-based approach
6. **Registration**: Registers multiple series of same modality
7. **Quality Control**: Checks SNR, contrast, and data integrity
8. **Patch Extraction**: Extracts overlapping patches with configurable size and overlap
9. **Data Saving**: Saves processed images, patches, and metadata

## Quality Control

The pipeline performs automatic quality control with the following checks:

- **Data Integrity**: Ensures sufficient non-zero voxels (>10%)
- **Signal-to-Noise Ratio**: Minimum SNR threshold of 2.0
- **Contrast**: Minimum contrast threshold of 0.1
- **Image Entropy**: Calculates information content

Failed QC images are logged but not included in final outputs.

## Troubleshooting

### Common Issues

1. **Missing Dependencies**: Install all required packages from `requirements.txt`
2. **Memory Issues**: Reduce patch size or increase system RAM
3. **DICOM Loading Errors**: Check DICOM file integrity and format
4. **Registration Failures**: Ensure images are from the same patient and modality

### Logging

Check the log files in the `logs/` directory for detailed processing information and error messages.

## Performance Considerations

- Processing time depends on image size, number of series, and system specifications
- Typical processing time: 30-120 seconds per patient
- Memory usage scales with image size and patch extraction parameters
- Consider using SSD storage for faster I/O operations

## License

This preprocessing pipeline is provided as-is for research purposes. Please ensure compliance with TCGA data usage policies.
