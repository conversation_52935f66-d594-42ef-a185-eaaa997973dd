#!/usr/bin/env python3
"""
Fix the metadata.json file by scanning the actual processed files.
"""

import json
import torch
from pathlib import Path

def fix_metadata():
    """Generate correct metadata from processed files."""
    
    processed_dir = Path("processed_tcga_data")
    patches_dir = processed_dir / "patches"
    
    metadata = {}
    
    # Scan all patch files
    for patch_file in patches_dir.glob("*.pt"):
        # Parse filename: TCGA-XX-XXXX_MODALITY.pt
        filename = patch_file.stem  # Remove .pt extension
        parts = filename.split('_')
        
        if len(parts) >= 2:
            patient_id = '_'.join(parts[:-1])  # Everything except last part
            modality = parts[-1].lower()  # Last part is modality
            
            # Load patches to get count
            try:
                patches = torch.load(patch_file)
                num_patches = len(patches)
                
                # Add to metadata
                if modality not in metadata:
                    metadata[modality] = {}
                
                if patient_id in metadata[modality]:
                    print(f"⚠️  Overwriting existing entry for {patient_id}")

                metadata[modality][patient_id] = {
                    'num_patches': num_patches,
                    'file': patch_file.name
                }

                print(f"✓ {patient_id} ({modality.upper()}): {num_patches} patches")
                print(f"   Current metadata keys: {list(metadata[modality].keys())}")
                
            except Exception as e:
                print(f"❌ Failed to load {patch_file}: {e}")
    
    # Save corrected metadata
    metadata_file = processed_dir / "metadata.json"
    print(f"\nSaving metadata with {len(metadata)} modalities...")
    for mod, patients in metadata.items():
        print(f"  {mod}: {len(patients)} patients")

    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
        f.flush()

    print(f"\n✅ Fixed metadata saved to: {metadata_file}")

    # Verify the file was written correctly
    with open(metadata_file, 'r') as f:
        saved_metadata = json.load(f)
        print(f"Verification: File contains {len(saved_metadata)} modalities")
    
    # Print summary
    total_patches = 0
    total_patients = 0
    for modality, patients in metadata.items():
        print(f"\n{modality.upper()} modality:")
        for patient_id, info in patients.items():
            print(f"  {patient_id}: {info['num_patches']} patches")
            total_patches += info['num_patches']
            total_patients += 1
    
    print(f"\nSummary:")
    print(f"Total patients: {total_patients}")
    print(f"Total patches: {total_patches}")
    
    return metadata

if __name__ == "__main__":
    fix_metadata()
